package s3manager

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// MockOvhProvider is a mock that implements the OvhApiProvider interface
type MockOvhProvider struct {
	execApiFunc func(method, path, body string) (any, error)
}

func (m *MockOvhProvider) ExecApi(method, path, body string) (any, error) {
	if m.execApiFunc != nil {
		return m.execApiFunc(method, path, body)
	}
	return nil, fmt.Errorf("mock not configured")
}

func TestOvhProjects(t *testing.T) {
	t.Run("list", func(t *testing.T) {
		// Save original stdout
		originalStdout := os.Stdout

		// Create a new pipe for this test
		r, w, err := os.Pipe()
		assert.NoError(t, err)

		// Redirect stdout to our pipe
		os.Stdout = w

		// Create channel and goroutine to capture output
		output := make(chan string)
		go func() {
			var buf bytes.Buffer
			io.Copy(&buf, r)
			output <- buf.String()
		}()

		// Create a mock provider that simulates the real API behavior
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				// First call: get project list
				if path == "/cloud/project" {
					return []any{"project-1", "project-2"}, nil
				}
				// Subsequent calls: get project details
				if path == "/cloud/project/project-1" {
					return map[string]any{
						"description": "k8s-1",
						"project_id":  "project-1",
					}, nil
				}
				if path == "/cloud/project/project-2" {
					return map[string]any{
						"description": "k8s-2",
						"project_id":  "project-2",
					}, nil
				}
				return nil, fmt.Errorf("unexpected API call: %s %s", method, path)
			},
		}

		// Test the actual logic
		err = listOvhProjects(mockProvider)
		assert.NoError(t, err)

		// Close writer to signal end of output
		w.Close()

		// Restore stdout before reading output
		os.Stdout = originalStdout

		// Read the captured output
		outputStr := <-output
		r.Close()

		// Verify the formatted output (not JSON, but the actual format from runOvhProjects)
		assert.Contains(t, outputStr, "k8s-1      project-1\n")
		assert.Contains(t, outputStr, "k8s-2      project-2\n")
	})

	t.Run("API error on project list", func(t *testing.T) {
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				if path == "/cloud/project" {
					return nil, fmt.Errorf("API connection failed")
				}
				return nil, fmt.Errorf("unexpected call")
			},
		}

		err := listOvhProjects(mockProvider)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to list OVH projects")
		assert.Contains(t, err.Error(), "API connection failed")
	})

	t.Run("API error on project details", func(t *testing.T) {
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				if path == "/cloud/project" {
					return []any{"project-1"}, nil
				}
				if path == "/cloud/project/project-1" {
					return nil, fmt.Errorf("project details failed")
				}
				return nil, fmt.Errorf("unexpected call")
			},
		}

		err := listOvhProjects(mockProvider)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get project details for project-1")
		assert.Contains(t, err.Error(), "project details failed")
	})

	t.Run("unexpected result format", func(t *testing.T) {
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				if path == "/cloud/project" {
					return "not-an-array", nil
				}
				return nil, fmt.Errorf("unexpected call")
			},
		}

		err := listOvhProjects(mockProvider)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unexpected result format")
	})

	t.Run("non-string project ID in list", func(t *testing.T) {
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				if path == "/cloud/project" {
					return []any{123}, nil // non-string element
				}
				return nil, fmt.Errorf("unexpected call")
			},
		}

		err := listOvhProjects(mockProvider)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "resultList element 0")
		assert.Contains(t, err.Error(), "unexpected type")
	})
}
