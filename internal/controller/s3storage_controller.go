package controller

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	logf "sigs.k8s.io/controller-runtime/pkg/log"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
)

// S3StorageReconciler reconciles a S3Storage object
type S3StorageReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages/finalizers,verbs=update
// +kubebuilder:rbac:groups="",resources=secrets,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.20.4/pkg/reconcile
func (r *S3StorageReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := logf.Log.WithName(fmt.Sprintf("%s/%s", req.Namespace, req.Name))
	log.Info("Reconcile triggered")

	// Fetch the S3Storage resource
	s3storage := &s3managerv1.S3Storage{}
	err := r.Get(ctx, req.NamespacedName, s3storage)
	if err != nil {
		if errors.IsNotFound(err) {
			// S3Storage resource not found, could have been deleted
			log.Info("S3Storage resource not found, ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request
		log.Error(err, "Failed to get S3Storage")
		return ctrl.Result{}, err
	}

	// Create or update the secret with username and password
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s3storage.Name, // Secret name matches S3Storage name
			Namespace: s3storage.Namespace,
		},
		Type: corev1.SecretTypeOpaque,
		Data: map[string][]byte{
			"username": []byte("test"),
			"password": []byte("changeme"),
		},
	}

	// Set S3Storage instance as the owner of the secret
	if err := controllerutil.SetControllerReference(s3storage, secret, r.Scheme); err != nil {
		log.Error(err, "Failed to set controller reference for secret")
		return ctrl.Result{}, err
	}

	// Check if the secret already exists
	existingSecret := &corev1.Secret{}
	err = r.Get(ctx, client.ObjectKey{Name: secret.Name, Namespace: secret.Namespace}, existingSecret)
	if err != nil && errors.IsNotFound(err) {
		// Secret doesn't exist, create it
		log.Info("Creating secret", "secret", secret.Name)
		if err := r.Create(ctx, secret); err != nil {
			log.Error(err, "Failed to create secret")
			return ctrl.Result{}, err
		}
		log.Info("Successfully created secret", "secret", secret.Name)
	} else if err != nil {
		// Error reading the secret
		log.Error(err, "Failed to get secret")
		return ctrl.Result{}, err
	} else {
		// Secret exists, update it if needed
		if string(existingSecret.Data["username"]) != "test" || string(existingSecret.Data["password"]) != "changeme" {
			log.Info("Updating secret", "secret", secret.Name)
			existingSecret.Data = secret.Data
			if err := r.Update(ctx, existingSecret); err != nil {
				log.Error(err, "Failed to update secret")
				return ctrl.Result{}, err
			}
			log.Info("Successfully updated secret", "secret", secret.Name)
		}
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *S3StorageReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&s3managerv1.S3Storage{}).
		Named("s3storage").
		Complete(r)
}
