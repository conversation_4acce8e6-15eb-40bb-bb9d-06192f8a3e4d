package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
)

func TestS3StorageController(t *testing.T) {
	// Create a fake client and context for testing
	testScheme := runtime.NewScheme()
	err := s3managerv1.AddToScheme(testScheme)
	assert.NoError(t, err)
	err = corev1.AddToScheme(testScheme)
	assert.NoError(t, err)
	k8sClient := fake.NewClientBuilder().WithScheme(testScheme).Build()
	ctx := context.Background()

	reconciler := &S3StorageReconciler{
		Client: k8sClient,
		Scheme: k8sClient.Scheme(),
	}
	s3StorageResourceKey := client.ObjectKey{
		Namespace: "default",
		Name:      "test",
	}
	s3StorageResource := &s3managerv1.S3Storage{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "test",
		},
		Spec: s3managerv1.S3StorageSpec{
			Provider: "ovh",
			Region:   "eu-west-par",
		},
	}

	t.Run("Create S3Storage resource", func(t *testing.T) {
		assert.NoError(t, k8sClient.Create(ctx, s3StorageResource))
		// Verify the created resource
		var resource s3managerv1.S3Storage
		err := k8sClient.Get(ctx, s3StorageResourceKey, &resource)
		assert.NoError(t, err)
		assert.Equal(t, resource.Spec.Provider, "ovh")
		assert.Equal(t, resource.Spec.Region, "eu-west-par")
	})

	t.Run("Reconcile no resources", func(t *testing.T) {
		_, err := reconciler.Reconcile(ctx, reconcile.Request{
			NamespacedName: s3StorageResourceKey,
		})
		assert.NoError(t, err)
	})

	t.Run("Reconcile creates secret", func(t *testing.T) {
		// Create a fresh S3Storage resource for this test
		testResource := &s3managerv1.S3Storage{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "default",
				Name:      "test-secret",
			},
			Spec: s3managerv1.S3StorageSpec{
				Provider: "ovh",
				Region:   "eu-west-par",
			},
		}
		assert.NoError(t, k8sClient.Create(ctx, testResource))

		// Reconcile the resource
		_, err := reconciler.Reconcile(ctx, reconcile.Request{
			NamespacedName: client.ObjectKey{
				Namespace: "default",
				Name:      "test-secret",
			},
		})
		assert.NoError(t, err)

		// Verify the secret was created
		secret := &corev1.Secret{}
		err = k8sClient.Get(ctx, client.ObjectKey{
			Namespace: "default",
			Name:      "test-secret",
		}, secret)
		assert.NoError(t, err)

		// Verify secret contents
		assert.Equal(t, "test", string(secret.Data["username"]))
		assert.Equal(t, "changeme", string(secret.Data["password"]))

		// Verify owner reference
		assert.Len(t, secret.OwnerReferences, 1)
		assert.Equal(t, "S3Storage", secret.OwnerReferences[0].Kind)
		assert.Equal(t, "test-secret", secret.OwnerReferences[0].Name)
	})
}
