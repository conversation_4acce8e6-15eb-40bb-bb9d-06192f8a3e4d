package ovh

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/ovh/go-ovh/ovh"
)

// Must implement S3Provider interface (from internal/providers/interface.go)
type Provider struct {
	endpoint          string
	applicationKey    string
	applicationSecret string
	consumerKey       string
	projectId         string
	client            *ovh.Client
}

func NewProvider(
	endpoint, applicationKey, applicationSecret, consumerKey, projectId string,
) (Provider, error) {
	// Check if OVH credentials are configured
	if applicationKey == "" {
		return Provider{}, fmt.Errorf("OVH application key not configured. Set OVH_APPLICATION_KEY environment variable or use --ovh-application-key flag")
	}

	if applicationSecret == "" {
		return Provider{}, fmt.Errorf("OVH application secret not configured. Set OVH_APPLICATION_SECRET environment variable or use --ovh-application-secret flag")
	}

	if consumerKey == "" {
		return Provider{}, fmt.Errorf("OVH consumer key not configured. Set OVH_CONSUMER_KEY environment variable or use --ovh-consumer-key flag")
	}

	provider := Provider{
		endpoint:          endpoint,
		applicationKey:    applicationKey,
		applicationSecret: applicationSecret,
		consumerKey:       consumerKey,
		projectId:         projectId,
	}
	client, err := ovh.NewClient(
		provider.endpoint,
		provider.applicationKey,
		provider.applicationSecret,
		provider.consumerKey,
	)
	if err != nil {
		return provider, fmt.Errorf("failed to create OVH client: %w", err)
	}
	provider.client = client

	return provider, nil
}

func (p *Provider) GetName() (string, error) {
	return "ovh", nil
}

func (p *Provider) GetClient() (*ovh.Client, error) {
	return ovh.NewClient(
		p.endpoint,
		p.applicationKey,
		p.applicationSecret,
		p.consumerKey,
	)
}

func (p *Provider) getProjectId() (string, error) {
	if p.projectId == "" {
		return "", fmt.Errorf("OVH project id not configured. Set OVH_PROJECT_ID environment variable or use --ovh-project-id flag")
	}
	return p.projectId, nil
}

func (p *Provider) ExecApi(method string, path string, body string) (any, error) {
	method = strings.ToUpper(method)

	// Make the API call
	var result any
	var callErr error

	switch method {
	case "GET":
		callErr = p.client.Get(path, &result)
	case "POST":
		if body != "" {
			var requestBody any
			if err := json.Unmarshal([]byte(body), &requestBody); err != nil {
				return nil, fmt.Errorf("invalid JSON body: %w", err)
			}
			callErr = p.client.Post(path, requestBody, &result)
		} else {
			callErr = p.client.Post(path, nil, &result)
		}
	case "PUT":
		if body != "" {
			var requestBody any
			if err := json.Unmarshal([]byte(body), &requestBody); err != nil {
				return nil, fmt.Errorf("invalid JSON body: %w", err)
			}
			callErr = p.client.Put(path, requestBody, &result)
		} else {
			callErr = p.client.Put(path, nil, &result)
		}
	case "DELETE":
		callErr = p.client.Delete(path, &result)
	default:
		return nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	if callErr != nil {
		return nil, callErr
	}

	return result, nil
}

func (p *Provider) ExecApiProject(method string, path string, body string) (any, error) {
	projectId, err := p.getProjectId()
	if err != nil {
		return nil, err
	}
	projectEndpoint := fmt.Sprintf(ProjectEndpoint, projectId)
	path = fmt.Sprintf("%s%s", projectEndpoint, path)
	return p.ExecApi(method, path, body)
}
