package ovh

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// MockOvhProvider is a simple mock that implements the ExecApi method
type MockOvhProvider struct {
	execApiFunc func(method, path, body string) (any, error)
}

func (m *MockOvhProvider) ExecApi(method, path, body string) (any, error) {
	if m.execApiFunc != nil {
		return m.execApiFunc(method, path, body)
	}
	return nil, fmt.Errorf("mock not configured")
}

func TestNewProvider(t *testing.T) {
	t.Run("no application key", func(t *testing.T) {
		_, err := NewProvider("ovh-eu", "", "secret", "consumer", "project")
		assert.ErrorContains(t, err, "OVH application key not configured")
	})

	t.Run("no application secret", func(t *testing.T) {
		_, err := NewProvider("ovh-eu", "key", "", "consumer", "project")
		assert.ErrorContains(t, err, "OVH application secret not configured")
	})

	t.Run("no consumer key", func(t *testing.T) {
		_, err := NewProvider("ovh-eu", "key", "secret", "", "project")
		assert.ErrorContains(t, err, "OVH consumer key not configured")
	})
}

func TestExecApi(t *testing.T) {
	t.Run("get cloud project", func(t *testing.T) {
		// Create a mock provider with expected response
		expectedProjects := []string{"project-id-1", "project-id-2", "project-id-3"}
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				assert.Equal(t, method, "get")
				assert.Equal(t, path, "/cloud/project")
				assert.Equal(t, body, "")
				return expectedProjects, nil
			},
		}
		// Test the mock function directly
		result, err := mockProvider.ExecApi("get", "/cloud/project", "")
		assert.NoError(t, err)
		assert.Equal(t, result, expectedProjects)
	})

	t.Run("get cloud project ko", func(t *testing.T) {
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				return nil, fmt.Errorf("API connection failed")
			},
		}
		result, err := mockProvider.ExecApi("get", "/cloud/project", "")
		assert.Error(t, err)
		assert.ErrorContains(t, err, "API connection failed")
		assert.Nil(t, result)
	})

}
